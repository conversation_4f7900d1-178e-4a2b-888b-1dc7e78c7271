apiVersion: apps/v1
kind: Deployment
metadata:
  name: python-worker
  labels:
    app: kidaura
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kidaura
  template:
    metadata:
      labels:
        app: kidaura
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: python-worker
          image: registry.gitlab.com/kidaura/python-worker/python-worker:latest
          imagePullPolicy: "Always"
      imagePullSecrets:
        - name: gitlab-credentials
