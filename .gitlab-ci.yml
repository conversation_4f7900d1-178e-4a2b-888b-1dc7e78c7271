stages:
  - build
  - deploy

build:worker:
  stage: build
  interruptible: true
  image: docker:cli
  services:
    - docker:dind
  before_script:
    - cat $VARS > .env
    - export DOCKER_REGISTRY_USER=$CI_REGISTRY_USER
    - export DOCKER_REGISTRY_PASSWORD=$CI_REGISTRY_PASSWORD
    - export DOCKER_REGISTRY_URL=$CI_REGISTRY
    - export IMAGE_NAME_WITH_REGISTRY_PREFIX=$CI_REGISTRY_IMAGE
    - docker login -u "$DOCKER_REGISTRY_USER" -p "$DOCKER_REGISTRY_PASSWORD" $DOCKER_REGISTRY_URL

  script:
    - echo "Building..."
    - export CONTAINER_FULL_IMAGE_NAME_WITH_TAG=$IMAGE_NAME_WITH_REGISTRY_PREFIX/python-worker:latest
    - docker build -f ./Dockerfile --cache-from $CONTAINER_FULL_IMAGE_NAME_WITH_TAG --pull -t python-worker .
    - docker tag python-worker "$CONTAINER_FULL_IMAGE_NAME_WITH_TAG"
    - docker push $IMAGE_NAME_WITH_REGISTRY_PREFIX/python-worker:latest
    - echo $CONTAINER_FULL_IMAGE_NAME_WITH_TAG

.deploy:
  image:
    name: amazon/aws-cli
    entrypoint: [""]
  stage: deploy
  services:
    - docker:dind
  before_script:
    - apk update && apk add --no-cache curl
    - curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.22.5/bin/linux/amd64/kubectl
    - chmod +x ./kubectl && mv ./kubectl /usr/local/bin/kubectl
    - mkdir -p $HOME/.kube
    - cat $KUBE_CONFIG > $HOME/.kube/config
    - kubectl config set-context --current --namespace=default

deploy:worker:
  extends: .deploy
  script:
    - kubectl apply -f kube/worker.yml
    - kubectl rollout restart deployment python-worker
