import moviepy.editor as mp
from .utils import *
from pqdm.processes import pqdm
import os
import json
import warnings
import matplotlib.pyplot as plt
import pandas as pd
from PIL import Image
import numpy as np
import traceback
import io
import matplotlib
from moviepy.video.io.bindings import mplfig_to_npimage

matplotlib.use("Agg")


warnings.filterwarnings("ignore")


# json name list
json_name_list = []

# get json file name
ext = ".json"
path_of_the_directory = "data/raw/Tracing/ASD/SP023"
for path, dirc, files in os.walk(path_of_the_directory):
    for name in files:
        if name.endswith(ext):
            json_name_list.append(f"{path}/{name}")

sum_datasets_list = []

file_name = "file_name"
velocity_mean = 0
total_distance = 0
total_time = 0
total_area = 0
shortest_drag_touchdata = "0"
longest_drag_touchdata = "0"

result_path = os.path.join(os.getcwd(), "data/processed")


def filter_data(data):
    for k in dict(data["json"]["touchData"]).keys():
        phases = [p["touchPhase"] for p in data["json"]["touchData"][k]]
        if len(data["json"]["touchData"][k]) == 1:
            del data["json"]["touchData"][k]
        elif len(set(phases)) == 1:
            del data["json"]["touchData"][k]
        elif ("Began" not in set(phases) or "Ended" not in set(phases)) and (
            "B" not in set(phases) or "E" not in set(phases)
        ):
            del data["json"]["touchData"][k]
        else:
            data["json"]["touchData"][k] = list(
                filter(
                    lambda x: (x["x"] < 2000 or x["y"] < 2000),
                    data["json"]["touchData"][k],
                )
            )
    return data


def is_cake_color(touchData):
    zones = set()
    cake_zones = ["BottomIcing", "TopIcing", "BottomCake", "TopCake"]
    for i in touchData.values():
        for z in i:
            zones.add(z["zone"])
    return any(z for z in cake_zones if z in zones)


def plot_game_data(file):
    data = filter_data(json.loads(open(file).read()))
    game_type = data["json"]["dataSet"]
    is_cake_game = is_cake_color(data["json"]["touchData"])

    target_group = file.split("/")[3]
    sid = file.split("/")[4]
    file_name = file.split("/")[5].split(".")[0]

    save_path = f"data/processed/images/{game_type}/{target_group}/"
    tracing_plot_index = (0, 1, 2, 3)
    if game_type == "Tracing":
        fig, ax = plt.subplots(1, 4, figsize=(15, 15))
    else:
        fig, ax = plt.subplots()
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    colors = plt.cm.binary(np.linspace(0, 1, len(data["json"]["touchData"].values())))
    for i, values in enumerate(data["json"]["touchData"].values()):
        df = pd.DataFrame(values)
        if game_type == "Tracing":
            cam = df["camFrame"][0]
            ax[tracing_plot_index[cam]].plot(
                df["x"], df["y"], linewidth=3, color="black"
            )
        if game_type == "Coloring":
            ax.plot(df["x"], df["y"], linewidth=3, color=colors[i])

    if game_type == "Coloring":
        ax.set_xlim([0, 2048])
        ax.set_ylim([0, 1396])
        if is_cake_game:
            img = Image.open("data/images/game/C2.png")
        else:
            img = Image.open("data/images/game/C1.png")
        img = np.array(img.convert("L"))
        ax.imshow(img[::-1], origin="lower", cmap="gray")
        ax.axis("off")
    else:
        plt.subplots_adjust(wspace=0, hspace=0)
        for c in tracing_plot_index:
            img = Image.open(f"data/images/game/T{c+1}.png")
            img = np.array(img.convert("L"))
            ax[c].imshow(img[::-1], origin="lower", cmap="gray")
            ax[c].axis("off")
            ax[c].set_xlim([0, 2048])
            ax[c].set_ylim([0, 1396])
    plt.savefig(f"{save_path}/{sid}_{file_name}.png", bbox_inches="tight")
    plt.close()
    return f"{save_path}/{sid}_{file_name}.png"


def create_video(image_files, timestamps, output_video_path):
    clips = []

    for i, image_file in enumerate(image_files):
        # Load the image using MoviePy
        img = mp.ImageClip(image_file)

        # Set the duration of the image using timestamps
        if i < len(timestamps) - 1:
            duration = timestamps[i + 1] - timestamps[i]
        else:
            duration = 1  # Default duration if there's no next timestamp

        img = img.set_duration(duration)
        clips.append(img)

    print("Concatenating video...")
    # Concatenate the clips to create the final video
    video = mp.concatenate_videoclips(clips, method="compose")

    # Write the video to the specified output path
    video.write_videofile(output_video_path, codec="libx264", fps=5, verbose=True)


def load_image(file_path):
    with Image.open(file_path) as img:
        return np.array(img.convert("L"))


def make_cake_game_plot(ax):
    img = load_image("data/images/game/C2.png")
    ax.imshow(img[::-1], origin="lower", cmap="gray")
    ax.axis("off")


def make_non_cake_game_plot(ax):
    img = load_image("data/images/game/C1.png")
    ax.imshow(img[::-1], origin="lower", cmap="gray")
    ax.axis("off")


def make_tracing_game_plot(ax, c):
    img = load_image(f"data/images/game/T{c+1}.png")
    ax[c].imshow(img[::-1], origin="lower", cmap="gray")
    ax[c].axis("off")
    ax[c].set_xlim([0, 2048])
    ax[c].set_ylim([0, 1396])


def plot_line(row, df, ax):
    i = row.name
    if i < len(df) - 1:
        row_next = df.iloc[i + 1]
        if not ((row["x"] == row_next["x"]) and (row["y"] == row_next["y"])):
            ax.plot(
                [row["x"], row_next["x"]],
                [row["y"], row_next["y"]],
                linewidth=3,
                color="black",
            )


def create_game_video(file):
    with open(file, "r") as f:
        data = filter_data(json.load(f))

    game_type = data["json"]["dataSet"]
    is_cake_game = is_cake_color(data["json"]["touchData"])

    file_split = file.split("/")
    target_group = file_split[3]
    sid = file_split[4]

    file_name = file_split[5].replace(".json", "")

    save_path = f"data/processed/videos/{game_type}/{target_group}/"

    # skip i the file exists
    if os.path.exists(f"{save_path}{sid}_{file_name}.mp4"):
        return f"{save_path}{sid}_{file_name}.mp4"

    if not os.path.exists(save_path):
        os.makedirs(save_path)

    values = data["json"]["touchData"]
    startTime = data["json"]["startTime"]
    tracing_plot_index = (0, 1, 2, 3)

    frames = []
    timestamps = []

    if game_type == "Tracing":
        fig, ax = plt.subplots(1, 4, figsize=(15, 15))
        plt.subplots_adjust(wspace=0, hspace=0)
        for c in tracing_plot_index:
            make_tracing_game_plot(ax, c)
    elif game_type == "Coloring":
        fig, ax = plt.subplots()
        ax.set_xlim([0, 2048])
        ax.set_ylim([0, 1396])
        if is_cake_game:
            make_cake_game_plot(ax)
        else:
            make_non_cake_game_plot(ax)

    for i, values in enumerate(values.values()):
        df = pd.DataFrame(values)
        if game_type == "Tracing":
            df["Line"] = df.apply(
                lambda row: plot_line(row, df, ax[row["camFrame"]]), axis=1
            )
        else:
            df["Line"] = df.apply(lambda row: plot_line(row, df, ax), axis=1)
        image_obj = io.BytesIO()
        plt.savefig(image_obj, format="png", bbox_inches="tight")
        pil_image = Image.open(image_obj)
        frames.append(pil_image)
        timestamps.append(df.iloc[-1]["time"] - startTime)
    plt.close(fig)

    clips = [np.array(f) for f in frames]
    save_path = f"{save_path}{sid}_{file_name}.mp4"
    create_video(clips, timestamps, save_path)

    return save_path


def create_game_video_by_points(file):

    data = filter_data(json.loads(open(file).read()))
    game_type = data["json"]["dataSet"]
    is_cake_game = is_cake_color(data["json"]["touchData"])

    file_split = file.split("/")
    target_group = file_split[3]
    sid = file_split[4]

    file_name = file_split[5].replace(".json", "")

    save_path = f"data/processed/videos3/{game_type}/{target_group}/"

    values = data["json"]["touchData"]

    startTime = data["json"]["startTime"]

    x_value = values
    tracing_plot_index = (0, 1, 2, 3)

    images = []
    timestamps = []

    # skip i the file exists
    if os.path.exists(f"{save_path}{sid}_{file_name}.mp4"):
        return f"{save_path}{sid}_{file_name}.mp4"

    if not os.path.exists(save_path):
        os.makedirs(save_path)

    print(f"Processing {file}")

    def init():
        if game_type == "Tracing":
            fig, ax = plt.subplots(1, 4, figsize=(15, 15))

            plt.subplots_adjust(wspace=0, hspace=0, left=0, right=1, bottom=0, top=1)
            for c in tracing_plot_index:
                # img = Image.open(f"data/images/game/T{c+1}.png")
                # img = np.array(img.convert('L'))
                # ax[c].imshow(img[::-1], origin='lower', cmap='gray')
                ax[c].axis("off")
                ax[c].set_xlim([0, 2048])
                ax[c].set_ylim([0, 1396])
        elif game_type == "Coloring":
            fig, ax = plt.subplots()
            ax.set_xlim([0, 2048])
            ax.set_ylim([0, 1396])
            # if is_cake_game:
            #     img = Image.open('data/images/game/C2.png')
            # else:
            #     img = Image.open('data/images/game/C1.png')
            # img = np.array(img.convert('L'))
            # ax.imshow(img[::-1], origin='lower', cmap='gray')
            ax.axis("off")
        fig.tight_layout(pad=0, w_pad=0.0)
        return ax, fig

    ax, fig = init()
    for i, values in enumerate(data["json"]["touchData"].values()):

        if not os.path.exists(save_path):
            os.makedirs(save_path)
        df = pd.DataFrame(values)

        print(f"{i}/ {len(data['json']['touchData'])}")
        if game_type == "Coloring":
            # iterate throuth subsequent 2 rows and plot line between those 2 points
            for i in range(0, len(df) - 1):
                row = df.iloc[i]
                row_next = df.iloc[i + 1]
                if (row["x"] == row_next["x"]) and (row["y"] == row_next["y"]):
                    continue
                ax.plot(
                    [row["x"], row_next["x"]],
                    [row["y"], row_next["y"]],
                    linewidth=3,
                    color="black",
                )
                fig.canvas.draw()
                img = Image.frombytes(
                    "RGB", fig.canvas.get_width_height(), fig.canvas.tostring_rgb()
                )
                images.append(img)
                timestamps.append(row_next["time"] - startTime)
        elif game_type == "Tracing":
            for i in range(0, len(df) - 1):
                row = df.iloc[i]
                row_next = df.iloc[i + 1]
                cam = row["camFrame"]
                if (row["x"] == row_next["x"]) and (row["y"] == row_next["y"]):
                    continue
                ax[cam].plot(
                    [row["x"], row_next["x"]],
                    [row["y"], row_next["y"]],
                    linewidth=3,
                    color="black",
                )
                fig.canvas.draw()
                img = Image.frombytes(
                    "RGB", fig.canvas.get_width_height(), fig.canvas.tostring_rgb()
                )
                images.append(img)
                timestamps.append(row_next["time"] - startTime)
    plt.close()

    clips = [np.array(f) for f in images]
    print("Creating Video...")
    create_video(clips, timestamps, f"{save_path}{sid}_{file_name}.mp4")
    return f"{save_path}{sid}_{file_name}.mp4"


def process_raw_data(data, game_type=1):
    # color game = 1
    # tracing game = 0
    try:

        data = filter_data(data)

        # Iterating through the json
        # list
        # json parser.
        df = pd.DataFrame()
        start_time = data["json"]["startTime"]
        for i in data["json"]["touchData"]:
            temp_row = data["json"]["touchData"][i]
            for data_by_id in temp_row:
                temp_df = pd.DataFrame(
                    data_by_id,
                    index=[
                        "i",
                    ],
                )
                temp_df["touchData"] = i
                df = pd.concat((df, temp_df))

        # Closing file

        # if df is empty, return empty list.
        if df.empty:
            return []
        # we have df and we need to calculate kinematics information

        df_vel = calculate_velocity(dataframe=df)

        df_acc = calculate_acc(dataframe=df_vel)
        df_kinematics = calculate_jerk(dataframe=df_acc)

        # calcualte deacc. Just a list.
        deacc = drag_deacceleration_calculator(dataframe=df_kinematics)
        # calculate response information
        response_df = response_info_calculator(
            dataframe=df_kinematics, start_time=start_time
        )
        # get peak velocity information.
        max_vel = get_max_vel_time(dataframe=df_kinematics)
        # concat response info and peak vel information
        response_peak_vel_df = pd.concat(
            [response_df, max_vel[["Max Velocity", "Max Vel Reached Time"]]], axis=1
        )

        # calculate drag distance, area duration information.
        distance = drag_distance_calculator(dataframe=df_kinematics)
        duration = drag_duration_calculator(dataframe=df_kinematics)
        areas = drag_area_calculator(dataframe=df_kinematics)
        # create a dataframe for distance duration and areas
        distance_information = pd.DataFrame(
            distance, columns=["touchid", "zone", "drag_distance"]
        )
        duration_information = pd.DataFrame(
            duration, columns=["touchid", "zone", "drag_duration"]
        )
        # area dataframe contain height and width information.
        drag_area = pd.DataFrame(areas, columns=["touchid", "area", "height", "width"])

        # drag information dataframe
        drag_information = pd.concat(
            [distance_information, duration_information["drag_duration"]], axis=1
        )

        # shortest and longest drag dataframes.
        shortest_drag_df, longest_drag_df = longest_shortest_drag_information(
            dataframe=df_kinematics
        )

        shortest_drag_velocity = 0
        shortest_drag_acc = 0
        shortest_drag_time = 0

        longest_drag_velocity = 0
        longest_drag_acc = 0
        longest_drag_time = 0

        if not shortest_drag_df.empty:
            shortest_drag_velocity = shortest_drag_df["Velocity"].mean()
            shortest_drag_acc = shortest_drag_df["ACC"].mean()
            shortest_drag_time = calculate_total_time(dataframe=shortest_drag_df)
        if not longest_drag_df.empty:
            longest_drag_velocity = longest_drag_df["Velocity"].mean()
            longest_drag_acc = longest_drag_df["ACC"].mean()
            longest_drag_time = calculate_total_time(dataframe=longest_drag_df)

        # calculate tap information and creata a df
        tap_info = tap_press_calculator(dataframe=df_kinematics)

        # merge tap_info, drag duration_information and drag distance_information
        user_behavior_df = pd.concat(
            [
                tap_info,
                duration_information["drag_duration"],
                distance_information["drag_distance"],
            ],
            axis=1,
        )
        completionPerc = np.nan
        completionDistance = 0
        error_total_distance = np.nan
        color_count = np.nan
        average_bmi = np.nan
        if game_type == 1:
            bmi_df = bmi_df_calculator(dataframe=df_kinematics)
            average_bmi = bmi_df["bmi_score"].mean()
            error_total_distance = bmi_df["error_total_distance"].sum()
            color_count = len(bmi_df["color_count"].unique())
            completionPerc = df_kinematics["completionPerc"].max()
        elif game_type == 0:
            completionDistance = df_kinematics["distance"].max()
        velocity_mean = df_kinematics["Velocity"].mean()
        acc_mean = df_kinematics["ACC"].mean()
        jerk_mean = df_kinematics["Jerk"].mean()
        total_distance = drag_information["drag_distance"].sum()
        total_time = calculate_total_time(dataframe=df_kinematics)
        total_area = drag_area["area"].sum()
        total_tap = user_behavior_df["Tap Count"].sum()
        total_press_count = user_behavior_df["Press Count"].sum()
        total_press_duration = user_behavior_df["Press Duration"].sum()

        if game_type == 1:
            return {
                "totalDistance": total_distance,
                "velocityMean": velocity_mean,
                "accMean": acc_mean,
                "jerkMean": jerk_mean,
                "totalTime": total_time,
                "totalArea": total_area,
                "averageBMI": average_bmi,
                "totalTap": total_tap,
                "totalPressCount": total_press_count,
                "totalPressDuration": total_press_duration,
                "completionPerc": completionPerc,
                "totalErrorDistance": error_total_distance,
                "colorCount": color_count,
                "shortestDragVelocity": shortest_drag_velocity,
                "shortestDragAcc": shortest_drag_acc,
                "shortestDragTime": shortest_drag_time,
                "longestDragVelocity": longest_drag_velocity,
                "longestDragAcc": longest_drag_acc,
                "longestDragTime": longest_drag_time,
            }
        else:
            return {
                "totalDistance": total_distance,
                "velocity_mean": velocity_mean,
                "accMean": acc_mean,
                "jerkMean": jerk_mean,
                "totalTime": total_time,
                "totalArea": total_area,
                "totalTap": total_tap,
                "totalPressCount": total_press_count,
                "totalPressDuration": total_press_duration,
                "completionDistance": completionDistance,
                "shortestDragVelocity": shortest_drag_velocity,
                "shortestDragAcc": shortest_drag_acc,
                "shortestDragTime": shortest_drag_time,
                "longestDragVelocity": longest_drag_velocity,
                "longestDragAcc": longest_drag_acc,
                "longestDragTime": longest_drag_time,
            }
    except Exception as e:
        print(e)
        # print traceback
        traceback.print_exc()
