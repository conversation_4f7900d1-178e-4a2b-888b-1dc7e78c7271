from typing import List, Any

import pandas as pd
import numpy as np
import math
import json
from shapely.geometry import Polygon


def calculate_total_time(dataframe: pd.DataFrame) -> int:
    """
    calculate total time duration for touchData
    :param dataframe:
    :return:

    """
    total_time = 0

    for i, touchdata in enumerate(dataframe["touchData"].unique()):

        temp_df = dataframe[dataframe["touchData"] == touchdata]

        # get time
        time_first = temp_df["time"].iloc[0]
        time_last = temp_df["time"].iloc[-1]

        time_diff = abs(time_first - time_last)

        total_time += time_diff

    return total_time


def get_max_vel_time(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    get peak velocity information.
    :param dataframe:
    :return:

    """

    information_list = []

    # loop for touchdata
    for i, touchdata in enumerate(dataframe["touchData"].unique()):

        temp_df = dataframe[dataframe["touchData"] == touchdata]

        get_max_vel = temp_df["Velocity"].max()
        get_max_vel_index = np.argmax(temp_df["Velocity"])

        first_time = temp_df["time"].iloc[0]
        max_vel_time = temp_df["time"].iloc[get_max_vel_index]

        time_diff = abs(first_time - max_vel_time)

        information_list.append([touchdata, get_max_vel, time_diff])

    max_vel_df = pd.DataFrame(
        information_list, columns=["TouchData", "Max Velocity", "Max Vel Reached Time"]
    )

    return max_vel_df


def get_x_y_1_locations(dataframe: pd.DataFrame, index: int) -> int:
    """

    :param dataframe:
    :return:

    x, x + 1 and y, y + 1 coordinates
    """

    # get x location in t  and t + 1 .
    x_1_loc = dataframe["x"].iloc[index + 1]
    x_loc = dataframe["x"].iloc[index]
    # get y location in t and t + 1
    y_1_loc = dataframe["y"].iloc[index + 1]
    y_loc = dataframe["y"].iloc[index]

    return x_1_loc, x_loc, y_1_loc, y_loc


def distance_calculator(x: int, x_1: int, y: int, y_1: int) -> int:
    """
    calculate  2 point distance

    :param x: x at t time
    :param x_1: x at t + 1 time
    :param y: y at t time
    :param y_1: y at t + 1 time
    :return:
    2 point distance

    """

    distance = math.sqrt(((x_1 - x) ** 2) + ((y_1 - y) ** 2))

    return distance


def bmi_df_calculator(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    Create a dataframe for calculation of bmi score and It contain  unique color count.

    :param dataframe:
    :return:

    return dataframe with information for calculation of bmi.
    """

    information_list = []

    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]

        out_to_zone = 0
        out_to_zone_distance = 0
        out_to_zone_time = 0

        zone_to_out = 0
        zone_to_out_distance = 0
        zone_to_out_time = 0

        all_time = 0
        color_count = len(temp_df["color"].unique())

        # if fingerId unique value sum bigger than 0, that mean fingerId value must be greater than 0.
        if temp_df["fingerId"].unique().sum() > 1:
            is_multitouch = 1
        else:
            is_multitouch = 0

        # loop in the temp_df index
        for temp_i in range(len(temp_df)):

            try:
                current_zone_name = temp_df["zone"].iloc[temp_i]
                next_zone_name = temp_df["zone"].iloc[temp_i + 1]

                current_time = temp_df["time"].iloc[temp_i]
                next_time = temp_df["time"].iloc[temp_i + 1]

                # calculate all_time.
                time_diff = abs(current_time - next_time)
                all_time += time_diff

                # get x and y location in t  and t + 1 .
                x_1_loc, x_loc, y_1_loc, y_loc = get_x_y_1_locations(
                    dataframe=temp_df, index=temp_i
                )
                # distance 2 point
                distance_2_point = distance_calculator(x_loc, x_1_loc, y_loc, y_1_loc)

                if current_zone_name == "Outside" and next_zone_name != "Outside":
                    # add 1 to counter
                    out_to_zone += 1
                    # add time in the out_to_zone_time
                    out_to_zone_time += time_diff
                    # add distance of 2 point
                    out_to_zone_distance += distance_2_point

                elif current_zone_name != "Outside" and next_zone_name == "Outside":
                    # add 1 to counter
                    zone_to_out += 1
                    # add time in the out_to_zone_time
                    zone_to_out_time += time_diff
                    # add distance of 2 point
                    zone_to_out_distance += distance_2_point

            except IndexError:
                information_list.append(
                    [
                        i,
                        is_multitouch,
                        color_count,
                        out_to_zone,
                        out_to_zone_time,
                        out_to_zone_distance,
                        zone_to_out,
                        zone_to_out_time,
                        zone_to_out_distance,
                        all_time,
                    ]
                )

    bmi_calculation_df = pd.DataFrame(
        information_list,
        columns=[
            "touchData",
            "is_multitouch",
            "color_count",
            "out_to_zone",
            "out_to_zone_time",
            "out_to_zone_distance",
            "zone_to_out",
            "zone_to_out_time",
            "zone_to_out_distance",
            "all_time",
        ],
    )

    bmi_calculation_df["error"] = (
        bmi_calculation_df["out_to_zone"] + bmi_calculation_df["zone_to_out"]
    )
    bmi_calculation_df["total_error_time"] = (
        bmi_calculation_df["out_to_zone_time"] + bmi_calculation_df["zone_to_out_time"]
    )

    bmi_calculation_df["out_to_zone_time_ratio"] = (
        bmi_calculation_df["out_to_zone_time"] / bmi_calculation_df["total_error_time"]
    )
    bmi_calculation_df["zone_to_out_time_ratio"] = (
        bmi_calculation_df["zone_to_out_time"] / bmi_calculation_df["total_error_time"]
    )

    bmi_calculation_df["zone_to_out_time_ratio"][
        np.isnan(bmi_calculation_df["zone_to_out_time_ratio"])
    ] = 0
    bmi_calculation_df["out_to_zone_time_ratio"][
        np.isnan(bmi_calculation_df["out_to_zone_time_ratio"])
    ] = 0

    bmi_calculation_df["error_total_distance"] = (
        bmi_calculation_df["out_to_zone_distance"]
        + bmi_calculation_df["zone_to_out_distance"]
    )

    bmi_calculation_df["per_average_error_distance"] = bmi_calculation_df[
        "error_total_distance"
    ] / (bmi_calculation_df["out_to_zone"] + bmi_calculation_df["zone_to_out"])
    bmi_calculation_df["per_average_error_distance"][
        np.isnan(bmi_calculation_df["per_average_error_distance"])
    ] = 0

    bmi_calculation_df["bmi_score"] = (
        bmi_calculation_df["error"] * bmi_calculation_df["per_average_error_distance"]
    )

    return bmi_calculation_df


def tap_press_calculator(dataframe: pd.DataFrame) -> list[list[Any]]:
    """
    Calculate some column. tap_counter, consecutive_tap_count,unique_tap_count, press_duration_time, press_count

    :param dataframe:
    :return:
    return a list with tap information
    """

    # tap and press list
    tap_info_list = []
    press_info_list = []

    # loop for touchdata
    for i, touchdata in enumerate(dataframe["touchData"].unique()):

        # tap counter
        tap_counter = 0

        # consecutive tap count
        consecutive_tap_count = 0

        # unique tap count flag for if-else
        unique_tap_count_flag = 0
        # unique count
        unique_tap_count = 0

        # press count
        press_count = 0
        # press duration
        press_duration = 0

        # create a temp_Df for filtered data.
        temp_df = dataframe[dataframe["touchData"] == touchdata]
        for zone in temp_df["zone"].unique():

            # create temp_tap_df for tep filter.
            temp_tap_df = temp_df[temp_df["zone"] == zone]
            touch_phase_list = temp_tap_df["touchPhase"].unique()

            if ["M"] not in touch_phase_list and ["Moved"] not in touch_phase_list:
                # get index error
                try:
                    tap_touch_id = int(dataframe["touchData"].unique()[i])
                    tap_next_touch_id = int(dataframe["touchData"].unique()[i + 1])

                    # consecutive tap
                    if (tap_touch_id + 1) == tap_next_touch_id:
                        consecutive_tap_count += 1
                except IndexError:
                    pass
            if ["B", "E", "C"] in list(touch_phase_list) or [
                "Began",
                "Ended",
                "Canceled",
            ] in list(touch_phase_list):
                tap_counter += 1

                if unique_tap_count_flag < 1:
                    unique_tap_count += 1
                    unique_tap_count_flag += 1

            elif ["B", "S", "E", "C"] in list(touch_phase_list) or [
                "Began",
                "Stationary",
                "Ended",
                "Canceled",
            ] in list(touch_phase_list):
                press_count += 1

                time_first = temp_tap_df["time"].iloc[0]
                time_last = temp_tap_df["time"].iloc[-1]
                time_diff = abs(time_first - time_last)

                press_duration += time_diff

            # append information to list.
            tap_info_list.append(
                [
                    touchdata,
                    zone,
                    tap_counter,
                    consecutive_tap_count,
                    unique_tap_count,
                    press_count,
                    press_duration,
                ]
            )

    tap_df = pd.DataFrame(
        tap_info_list,
        columns=[
            "touchData",
            "Zone",
            "Tap Count",
            "Consecutive Tap Count",
            "Unique Tap Count",
            "Press Count",
            "Press Duration",
        ],
    )

    return tap_df


def longest_shortest_drag_information(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    Detect longets and shortest drag later than get the information about them.

    :param dataframe:

    :return:
    return 2 dataframe. One of them longest other them shortest.
    """

    # get time diff. This diffrence refers to last touch - initial touch
    time_diff_list = []
    # loop in the touchData
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]

        if (
            "Moved" in temp_df["touchPhase"].unique()
            or "M" in temp_df["touchPhase"].unique()
        ):
            # get time difference
            time_diff = temp_df["time"].iloc[-1] - temp_df["time"].iloc[0]
            # append timediff information to list
            time_diff_list.append([i, time_diff])

    # list to dataframe
    time_diff_df = pd.DataFrame(time_diff_list, columns=["touchData", "time"])
    if len(time_diff_df) > 0:
        # get longest touchdata id
        longest_value_index = time_diff_df["time"].idxmax()
        longest_value = time_diff_df["touchData"].loc[longest_value_index]
        # get shortest touchData id
        shortest_value_index = time_diff_df["time"].idxmin()
        shortest_value = time_diff_df["touchData"].loc[shortest_value_index]

        # create dataframes for shortest and longest dataset.
        shortest_drag_df = dataframe[dataframe["touchData"] == shortest_value]
        longest_drag_df = dataframe[dataframe["touchData"] == longest_value]
    else:
        shortest_drag_df = pd.DataFrame()
        longest_drag_df = pd.DataFrame()
    # return it.
    return (
        shortest_drag_df,
        longest_drag_df,
    )


def response_info_calculator(
    dataframe: pd.DataFrame, start_time: int
) -> list[list[Any]]:
    """
    Calculate response feature.

    :param dataframe:
    :return:
    return 2 list. One of themn response list other one is initial touch list( initial touch - start time)
    """

    # creata lists for response and initial touch.
    response_list = []
    initial_touch_list = []
    # loop in the touchData
    for i, touchdata in enumerate(dataframe["touchData"].unique()):
        # filtered dataframe
        temp_response_df = dataframe[dataframe["touchData"] == touchdata]

        # get initial touch delay and appent to list.
        initial_touch_delay = abs(start_time - temp_response_df["time"].iloc[0])
        initial_touch_list.append([touchdata, initial_touch_delay])

        if (
            "Moved" in temp_response_df["touchPhase"].unique()
            or "M" in temp_response_df["touchPhase"].unique()
        ):

            try:
                # get next time information
                temp_response_next_df = dataframe[
                    dataframe["touchData"] == dataframe["touchData"].unique()[i + 1]
                ]
            except IndexError:
                temp_response_next_df = temp_response_df

            # get time information for touch delay information.
            start_time_drag = temp_response_df["time"][0]
            next_time_drag = temp_response_next_df["time"][0]

            time_diff = abs(start_time_drag - next_time_drag)

            try:
                response_list.append(
                    [touchdata, time_diff, dataframe["touchData"].unique()[i + 1]]
                )
            except IndexError:
                response_list.append([touchdata, time_diff, touchdata])

    touch_delay = pd.DataFrame(
        response_list, columns=["TouchData", "Touch-Drag Delay", "Next Drag"]
    )

    initial_touch = pd.DataFrame(
        initial_touch_list, columns=["touchData", "initial_touch_delay"]
    )

    response_df = pd.concat(
        [
            initial_touch[["touchData", "initial_touch_delay"]],
            touch_delay[["Touch-Drag Delay", "Next Drag"]],
        ],
        axis=1,
    )

    return response_df


def drag_deacceleration_calculator(dataframe: pd.DataFrame) -> list[list[Any]]:
    """
    Calculate drag deacceleration. I used deacc = (final vel -  initial vel) / time taken

    :param dataframe:
    :return:
    return list with deacceleration information
    """
    # create a deacceleration list
    deacceleration_list = []

    # loop in the touchData.
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        deacc_df = dataframe[dataframe["touchData"] == i]
        deacc_df["Velocity"] = np.nan_to_num(deacc_df["Velocity"], neginf=0)

        # I used  velocity mean beacuse it's more reliable than single data point.
        vel_1_loc = deacc_df["Velocity"].iloc[0:10].mean()
        vel_last_loc = deacc_df["Velocity"].iloc[-10:-1].mean()

        # get time information
        time_1_loc = deacc_df["time"].iloc[0]
        time_last_loc = deacc_df["time"].iloc[-1]

        # calculate differences.
        velocity_diff = vel_last_loc - vel_1_loc
        time = time_1_loc - time_last_loc
        # deacc = (final vel -  initial vel) / time taken
        deacc = velocity_diff / time
        # append deaccleration information to list.
        deacceleration_list.append([i, deacc])

    return deacceleration_list


def drag_duration_calculator(dataframe: pd.DataFrame) -> list[list[Any]]:
    """
    Calculate drag duration.

    :param dataframe:
    :return:
    return a list with drag duration information.
    """
    # create a duration list.
    duration_list = []
    # loop in the TouchData
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]

        # loop in the zone.
        for zone in temp_df["zone"].unique():
            # filtered dataframe. Filter is Zone.
            temp_drag_by_zone = temp_df[temp_df["zone"] == zone]
            # get first time
            first_time = temp_drag_by_zone["time"].iloc[0]
            # get last time
            last_time = temp_drag_by_zone["time"].iloc[-1]
            # get time difference
            time_diff = last_time - first_time

            # append duration information to list
            duration_list.append([i, zone, time_diff])

    return duration_list


def drag_distance_calculator(dataframe: pd.DataFrame) -> list[list[Any]]:
    """
    Calculate drag distance

    :param dataframe:
    :return:
    return a list with drag distance information.
    """

    # create a distance list
    distance_list = []
    # loop in the touchData
    for i in dataframe["touchData"].unique():
        # filtered data frame
        temp_df = dataframe[dataframe["touchData"] == i]
        # loop in the zone
        for zone in temp_df["zone"].unique():
            # set up the drag distance
            drag_distance = 0
            # filtered dataframe and filter is zone.
            temp_drag_by_zone = temp_df[temp_df["zone"] == zone]
            # loop for the filtered dataframe index.
            for j in range(len(temp_drag_by_zone)):
                # create a try, except for IndexError
                try:

                    # get x and y location in t  and t + 1 .
                    x_1_loc, x_loc, y_1_loc, y_loc = get_x_y_1_locations(
                        dataframe=temp_drag_by_zone, index=j
                    )
                    # calculate displacement
                    displacement = distance_calculator(x_loc, x_1_loc, y_loc, y_1_loc)
                    # add drag distance to drag distance variable
                    drag_distance += displacement
                except IndexError:
                    # append drag information to list.
                    distance_list.append([i, zone, drag_distance])

    return distance_list


def drag_area_calculator(dataframe: pd.DataFrame) -> list[list[Any]]:
    """
    Calculate drag area

    :param dataframe:
    :return:
    return a list with area information
    """
    # create a area list
    area_list = []
    # loop in the Touch Data
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]
        # if data point less than 3. It will be raise a error beacuse we cannot calculate  area of 2 points.
        if len(temp_df) < 3:
            pass
        else:
            # I used Polygon library for calculate the area
            polygon = Polygon(zip(temp_df["x"], temp_df["y"]))
            # get area information
            area = polygon.area
            # get bounds of area.
            bounds = polygon.bounds  # (minx, miny, maxx, maxy)
            # get height
            height = bounds[3] - bounds[1]
            # get width
            width = bounds[2] - bounds[0]
            # append area, heigh and width information to list
            area_list.append([i, area, height, width])

    return area_list


def calculate_jerk(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate jerk. I used jerk = change of acc / time for calculation.


    :param dataframe:
    :return:
    return dataframe with jerk information.
    """
    # create a empty dataframe for jerk.
    jerk_df = pd.DataFrame()
    # loop in the TouchData
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]
        # loop in the zone.
        for zone in temp_df["zone"].unique():
            # Filtered dataset and filter is zone.
            temp_zone_df = temp_df[temp_df["zone"] == zone]
            # create a new column and name is jerk and fill 0.
            temp_zone_df = temp_zone_df.assign(Jerk=0)
            # loop for the filtered data index.
            for j in range(len(temp_zone_df["time"])):
                # try except for IndexError
                try:
                    # get acc information
                    acc_1_loc = temp_zone_df["ACC"].iloc[j + 1]
                    acc_loc = temp_zone_df["ACC"].iloc[j]
                    # get time
                    time_1_loc = temp_zone_df["time"].iloc[j + 1]
                    time_loc = temp_zone_df["time"].iloc[1]
                    # get acc difff
                    acc_diff = acc_1_loc - acc_loc
                    time = time_1_loc - time_loc

                    # jerk = change of velocity / time
                    # try except for ZeroDivisionError error
                    try:
                        # calculate jerk
                        jerk = acc_diff / time
                        # assign to jerk information to dataframe.
                        temp_zone_df["Jerk"].iloc[j + 1] = jerk
                    except ZeroDivisionError:
                        # assign to jerk information to dataframe
                        temp_zone_df["Jerk"].iloc[j + 1] = 0

                except IndexError:
                    # our safe belt (when our displacement and  time  is equal to 0.0  (float) so our result become a
                    # nan)
                    temp_zone_df["Jerk"] = temp_zone_df["Jerk"].fillna(0)
                    # concat jerk df and temp zone df
                    jerk_df = pd.concat((jerk_df, temp_zone_df))

    jerk_df.replace([np.inf, -np.inf], 0, inplace=True)
    return jerk_df


def calculate_acc(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    Function calculate acc value with acc = change of velocity / time

    note:  when an object is slowing down, the acceleration is in the opposite direction as the velocity. Thus, this object has a negative acceleration.

    :param dataframe:

    :return: dataset with acc information
    return dataframe with acc information
    """
    # creata empty dataframe for acc df
    acc_df = pd.DataFrame()
    # loop in the touchData
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]
        # loop in the zone
        for zone in temp_df["zone"].unique():
            # filtered dataframe and filter is zone
            temp_zone_df = temp_df[temp_df["zone"] == zone]
            # create a acc column and fill 0
            temp_zone_df = temp_zone_df.assign(ACC=0)
            # loop for the filtered dataframe index
            for j in range(len(temp_zone_df["time"])):
                # try except for IndexError
                try:
                    # get velocity
                    vel_1_loc = temp_zone_df["Velocity"].iloc[j + 1]
                    vel_loc = temp_zone_df["Velocity"].iloc[j]
                    # get time
                    time_1_loc = temp_zone_df["time"].iloc[j + 1]
                    time_loc = temp_zone_df["time"].iloc[1]
                    # get velocity and time difference
                    velocity_diff = vel_1_loc - vel_loc
                    time = time_1_loc - time_loc

                    # acc = change of velocity / time
                    # try except for ZeroDivisionError error
                    try:
                        # calcualte acc
                        acc = velocity_diff / time
                        # assign to acc information to dataframe
                        temp_zone_df["ACC"].iloc[j + 1] = acc
                    except ZeroDivisionError:
                        # assign to acc information to dataframe
                        temp_zone_df["ACC"].iloc[j + 1] = 0

                except IndexError:
                    # our safe belt (when our displacement and  time  is equal to 0.0  (float) so our result become a nan)
                    temp_zone_df["ACC"] = temp_zone_df["ACC"].fillna(0)
                    # concat acc df and temp zone df
                    acc_df = pd.concat((acc_df, temp_zone_df))

    return acc_df


def calculate_velocity(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    calcualte velocity and velocity formula is v = displacement / time
    :param dataframe:

    :return:
    dataframe with velocity information
    """
    # create a dataframe for the vel dataframe
    vel_df = pd.DataFrame()
    # loop in the touchData
    for i in dataframe["touchData"].unique():
        # filtered dataframe
        temp_df = dataframe[dataframe["touchData"] == i]
        # loop in the zone
        for zone in temp_df["zone"].unique():
            # filtered dataframe and filter is zone
            temp_zone_df = temp_df[temp_df["zone"] == zone]
            # create a velocity column and fill 0
            temp_zone_df = temp_zone_df.assign(Velocity=0)
            # loop for the filtered dataframe index
            for j in range(len(temp_zone_df["time"])):
                # try except for IndexError
                try:
                    # get time
                    time_1_loc = temp_zone_df["time"].iloc[j + 1]
                    time_loc = temp_zone_df["time"].iloc[1]
                    # get x and y location in t  and t + 1 .
                    x_1_loc, x_loc, y_1_loc, y_loc = get_x_y_1_locations(
                        dataframe=temp_zone_df, index=j
                    )
                    # calcualte displacement
                    displacement = distance_calculator(x_loc, x_1_loc, y_loc, y_1_loc)
                    # get time diff
                    time = time_1_loc - time_loc

                    # v = d / t
                    # try except for ZeroDivisionError error
                    try:
                        # calculate velocity
                        velocity = displacement / time
                        # assign to Velocity information to dataframe
                        temp_zone_df["Velocity"].iloc[j + 1] = velocity
                    except ZeroDivisionError:
                        # assign to Velocity information to dataframe
                        temp_zone_df["Velocity"].iloc[j + 1] = 0

                except IndexError:
                    # our safe belt (when our displacement and  time  is equal to 0.0  (float) so our result become a
                    # nan)
                    temp_zone_df["Velocity"] = temp_zone_df["Velocity"].fillna(0)
                    # concat acc df and temp zone df
                    vel_df = pd.concat((vel_df, temp_zone_df))

    return vel_df


class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NpEncoder, self).default(obj)
