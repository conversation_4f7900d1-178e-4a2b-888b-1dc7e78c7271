import pika, sys, os
import json
from dotenv import load_dotenv
import pika.connection
from jobs import process_raw

load_dotenv()


broker_queue = "job-processor"

broker_response_queue = "job-processor-response"


def process_job(data):
    job_id = data.get("jobId")
    data = data.get("data")
    if not job_id:
        return
    if job_id == "process-coloring-results":
        # process data
        return process_raw(data, 1)
    elif job_id == "process-tracing-results":
        return process_raw(data, 0)
    return json.dumps({"status": "error", "data": "Invalid Job ID"})


def main():
    connection = pika.BlockingConnection(
        pika.connection.URLParameters(os.getenv("RABBITMQ_URL"))
    )
    channel = connection.channel()

    channel.queue_declare(queue=broker_queue)
    channel.queue_declare(queue=broker_response_queue)

    def callback(ch, method, properties, body):
        print(" [x] Received Message")
        try:
            json_data = json.loads(body)
            response = process_job(json_data)
        except Exception as e:
            print("Error", e)
            response = json.dumps({"status": "error", "data": "Error processing job"})

        # send back the response
        ch.basic_publish(
            exchange="",
            routing_key=broker_response_queue,
            body=response,
            properties=pika.BasicProperties(
                delivery_mode=2,
            ),
        )

    channel.basic_consume(
        queue=broker_queue, on_message_callback=callback, auto_ack=True
    )

    print(" [*] Waiting for messages.")
    channel.start_consuming()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("Interrupted")
        try:
            sys.exit(0)
        except SystemExit:
            os._exit(0)
