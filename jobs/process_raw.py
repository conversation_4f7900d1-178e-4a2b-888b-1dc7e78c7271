import json
from lib.process_features import process_raw_data
from lib.utils import NpEncoder
import joblib
import numpy as np


def process_raw(data, game_id=1):
    print(" [x] Processing Attrs")
    processed_data = process_raw_data(data["data"], game_id)
    age = data.get("age")
    gender = data.get("gender")
    print(" [x] Processed Data", processed_data)
    if game_id == 0:
        model = joblib.load("models/tracing_model_raw_v3.joblib")
    else:
        model = joblib.load("models/coloring_model_raw_v3.joblib")
    values = list(processed_data.values())
    values.append(age)
    values.append(gender)
    print(" [x] Values", values)
    predictions = model.predict_proba(np.array(values).reshape(1, -1))
    print(" [x] Predictions", predictions)
    print(np.argmax(predictions))

    response = {}
    response["predictions"] = predictions[0]
    response["score"] = predictions[0][np.argmax(predictions)]
    response["class"] = np.argmax(predictions)
    response["attrData"] = processed_data
    response["gameType"] = data["gameType"]
    response["subject"] = data["subjectId"]
    response["spFile"] = data["fileId"]
    return json.dumps(
        {"status": "success", "responseId": "raw-game-results", "data": response},
        cls=NpEncoder,
    )
